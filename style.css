* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Login Styles */
.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
}

.login-card h1 {
    color: #333;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
}

.subtitle {
    color: #666;
    font-size: 14px;
    margin-bottom: 30px;
}

.login-form {
    text-align: left;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
}

.input-wrapper input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    background: #fff;
    transition: all 0.3s ease;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.input-wrapper input::placeholder {
    color: #999;
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 12px 15px;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid #fcc;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(74, 144, 226, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

/* Main App Styles */
.main-container {
    width: 100%;
    max-width: 600px;
    padding: 20px;
}

.main-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.main-card h2 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    text-align: center;
}

.main-card .subtitle {
    text-align: center;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-group input[type="text"],
.form-group input[type="file"] {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    background: #fff;
    transition: all 0.3s ease;
}

.form-group input[type="text"]:focus {
    outline: none;
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group input[type="file"] {
    padding: 12px;
    cursor: pointer;
}

.submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(74, 144, 226, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

.logout-link {
    display: inline-block;
    margin-top: 20px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
    text-align: center;
    width: 100%;
}

.logout-link:hover {
    color: #4A90E2;
}

/* Process Page Styles */
.process-container {
    width: 100%;
    max-width: 800px;
    padding: 20px;
}

.process-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.success-message {
    background: #e8f5e8;
    color: #2d5a2d;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid #c3e6c3;
}

.success-message h2 {
    margin-bottom: 10px;
    font-size: 20px;
}

.download-link {
    background: #28a745;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    margin-top: 10px;
    transition: background 0.3s ease;
}

.download-link:hover {
    background: #218838;
}

.log-section {
    margin-top: 30px;
}

.log-section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
}

.log-section pre {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
    max-height: 400px;
    overflow-y: auto;
}

.error-card {
    background: #fee;
    color: #c33;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #fcc;
    text-align: center;
}

/* Responsive */
@media (max-width: 480px) {
    .login-container,
    .main-container,
    .process-container {
        padding: 10px;
    }
    
    .login-card,
    .main-card,
    .process-card {
        padding: 30px 20px;
    }
    
    .login-card h1,
    .main-card h2 {
        font-size: 22px;
    }
}
