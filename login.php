<?php
session_start();
require('config.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($_POST['senha'] === $painel_senha) {
        $_SESSION['autenticado'] = true;
        header("Location: index.php");
        exit;
    } else {
        $erro = "Senha incorreta.";
    }
}
?>
<!DOCTYPE html>
<html>
<head><title>Login</title></head>
<body>
    <h2>🔒 Acesso restrito</h2>
    <form method="POST">
        Senha: <input type="password" name="senha" required>
        <input type="submit" value="Entrar">
    </form>
    <?php if (isset($erro)) echo "<p style='color:red;'>$erro</p>"; ?>
</body>
</html>
