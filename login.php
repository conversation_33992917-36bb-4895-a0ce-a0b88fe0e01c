<?php
session_start();
require('config.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($_POST['senha'] === $painel_senha) {
        $_SESSION['autenticado'] = true;
        header("Location: index.php");
        exit;
    } else {
        $erro = "Senha incorreta.";
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - MegaInfoo Cloud</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="logo">
                <svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="18" fill="#4A90E2" stroke="#fff" stroke-width="2"/>
                    <rect x="12" y="25" width="3" height="8" fill="#FF8C00" rx="1"/>
                    <rect x="17" y="22" width="3" height="11" fill="#FF8C00" rx="1"/>
                    <rect x="22" y="18" width="3" height="15" fill="#FF8C00" rx="1"/>
                    <path d="M8 15 Q20 8 32 15" stroke="#fff" stroke-width="2" fill="none"/>
                    <text x="45" y="18" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#4A90E2">megainffo</text>
                    <text x="45" y="32" font-family="Arial, sans-serif" font-size="12" fill="#666">.cloud</text>
                </svg>
            </div>

            <h1>Bem-vindo</h1>
            <p class="subtitle">Faça login para acessar o sistema</p>

            <form method="POST" class="login-form">
                <div class="input-group">
                    <label for="senha">Senha</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <circle cx="12" cy="16" r="1"></circle>
                            <path d="m12 13 0 6"></path>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg>
                        <input type="password" id="senha" name="senha" placeholder="Digite sua senha" required>
                    </div>
                </div>

                <?php if (isset($erro)): ?>
                    <div class="error-message">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="15" y1="9" x2="9" y2="15"></line>
                            <line x1="9" y1="9" x2="15" y2="15"></line>
                        </svg>
                        <?php echo $erro; ?>
                    </div>
                <?php endif; ?>

                <button type="submit" class="login-btn">Entrar</button>
            </form>
        </div>
    </div>
</body>
</html>
