<?php
session_start();

// Verificar autenticação
if (!isset($_SESSION['autenticado'])) {
    http_response_code(403);
    die('<PERSON><PERSON> negado');
}

// Verificar se o arquivo foi solicitado
if (!isset($_GET['file']) || !isset($_GET['name'])) {
    http_response_code(400);
    die('Parâmetros inválidos');
}

$file_path = $_GET['file'];
$file_name = $_GET['name'];

// Validar se o arquivo existe e está na pasta temporária
if (!file_exists($file_path) || strpos(realpath($file_path), sys_get_temp_dir()) !== 0) {
    http_response_code(404);
    die('Arquivo não encontrado');
}

// Validar extensão do arquivo
$allowed_extensions = ['bat'];
$file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

if (!in_array($file_extension, $allowed_extensions)) {
    http_response_code(400);
    die('Tipo de arquivo não permitido');
}

// Definir headers para download
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . basename($file_name) . '"');
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Enviar arquivo
readfile($file_path);

// Opcional: Remover arquivo após download (descomente se desejar)
// unlink($file_path);

exit;
?>
