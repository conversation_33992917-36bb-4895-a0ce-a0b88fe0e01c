<?php
session_start();
if (!isset($_SESSION['autenticado'])) {
    header('Location: login.php');
    exit;
}
$token = bin2hex(random_bytes(16));
$_SESSION['token'] = $token;
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Implantar Cliente - MegaInfoo Cloud</title>
    <link rel="icon" type="image/png" href="img/icone.png">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="main-container">
        <div class="main-card">
            <div class="logo">
                <img src="img/LogoCompleto.png" alt="MegaInfoo Cloud" class="logo-img">
            </div>

            <h2>Nova Implantação de Cliente</h2>
            <p class="subtitle">Configure automaticamente um novo cliente no MikroTik</p>

            <form method="POST" action="process.php" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="cliente">Nome do Cliente</label>
                    <input type="text" id="cliente" name="cliente" placeholder="Ex: EMPRESA_ABC" required>
                </div>

                <div class="form-group">
                    <label for="ipbase">IP Base</label>
                    <input type="text" id="ipbase" name="ipbase" placeholder="Ex: 10.50.49" required>
                </div>

                <div class="form-group">
                    <label for="apelido">Apelido dos Secrets</label>
                    <input type="text" id="apelido" name="apelido" placeholder="Ex: abc" required>
                </div>

                <div class="form-group">
                    <label for="secrets_file">Planilha de usuários (.xlsx)</label>
                    <input type="file" id="secrets_file" name="secrets_file" accept=".xlsx" required>
                </div>

                <input type="hidden" name="token" value="<?php echo $token; ?>">
                <button type="submit" class="submit-btn">Enviar</button>
            </form>

            <a href="logout.php" class="logout-link">Sair do Sistema</a>
        </div>
    </div>
</body>
</html>
