<?php
session_start();
if (!isset($_SESSION['autenticado'])) {
    header('Location: login.php');
    exit;
}
$token = bin2hex(random_bytes(16));
$_SESSION['token'] = $token;
?>


<!DOCTYPE html>
<html>
<head><title>Implantar Cliente - Mikrotik</title></head>
<body>
    <h2>Nova Implantação de Cliente</h2>
    <form method="POST" action="process.php" enctype="multipart/form-data">
   

        Nome do Cliente: <input type="text" name="cliente" required><br><br>
        IP Base (ex: 10.50.49): <input type="text" name="ipbase" required><br><br>
        Apelido dos Secrets: <input type="text" name="apelido" required><br><br>
        Planilha de Secrets (.xlsx): <input type="file" name="secrets_file" accept=".xlsx" required><br><br>
        <input type="hidden" name="token" value="<?php echo $token; ?>">
        <input type="submit" value="Implantar Cliente">
    </form>
    <br>
    <a href="logout.php">Sair</a>
</body>
</html>
