<?php
session_start();
if (!isset($_SESSION['autenticado'])) {
    header('Location: login.php');
    exit;
}
$token = bin2hex(random_bytes(16));
$_SESSION['token'] = $token;
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Implantar Cliente - MegaInfoo Cloud</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="main-container">
        <div class="main-card">
            <div class="logo">
                <svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="18" fill="#4A90E2" stroke="#fff" stroke-width="2"/>
                    <rect x="12" y="25" width="3" height="8" fill="#FF8C00" rx="1"/>
                    <rect x="17" y="22" width="3" height="11" fill="#FF8C00" rx="1"/>
                    <rect x="22" y="18" width="3" height="15" fill="#FF8C00" rx="1"/>
                    <path d="M8 15 Q20 8 32 15" stroke="#fff" stroke-width="2" fill="none"/>
                    <text x="45" y="18" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#4A90E2">megainffo</text>
                    <text x="45" y="32" font-family="Arial, sans-serif" font-size="12" fill="#666">.cloud</text>
                </svg>
            </div>

            <h2>Nova Implantação de Cliente</h2>
            <p class="subtitle">Configure automaticamente um novo cliente no MikroTik</p>

            <form method="POST" action="process.php" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="cliente">Nome do Cliente</label>
                    <input type="text" id="cliente" name="cliente" placeholder="Ex: EMPRESA_ABC" required>
                </div>

                <div class="form-group">
                    <label for="ipbase">IP Base</label>
                    <input type="text" id="ipbase" name="ipbase" placeholder="Ex: 10.50.49" required>
                </div>

                <div class="form-group">
                    <label for="apelido">Apelido dos Secrets</label>
                    <input type="text" id="apelido" name="apelido" placeholder="Ex: abc" required>
                </div>

                <div class="form-group">
                    <label for="secrets_file">Planilha de Secrets (.xlsx)</label>
                    <input type="file" id="secrets_file" name="secrets_file" accept=".xlsx" required>
                </div>

                <input type="hidden" name="token" value="<?php echo $token; ?>">
                <button type="submit" class="submit-btn">🚀 Implantar Cliente</button>
            </form>

            <a href="logout.php" class="logout-link">Sair do Sistema</a>
        </div>
    </div>
</body>
</html>
