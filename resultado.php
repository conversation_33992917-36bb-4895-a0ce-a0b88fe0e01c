<?php
session_start();

// Verificar autenticação
if (!isset($_SESSION['autenticado'])) {
    header('Location: login.php');
    exit;
}

// Verificar se há dados de resultado
if (!isset($_SESSION['resultado'])) {
    header('Location: index.php');
    exit;
}

$resultado = $_SESSION['resultado'];

// Limpar dados da sessão após 5 minutos (segurança)
if (isset($resultado['timestamp']) && (time() - $resultado['timestamp']) > 300) {
    unset($_SESSION['resultado']);
    header('Location: index.php');
    exit;
}

// Limpar resultado da sessão para evitar reexibição
unset($_SESSION['resultado']);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $resultado['sucesso'] ? 'Implantação Concluída' : 'Erro na Implantação'; ?> - MegaInfoo Cloud</title>
    <link rel="icon" type="image/png" href="img/icone.png">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="process-container">
        <div class="process-card">
            <div class="logo">
                <img src="img/LogoCompleto.png" alt="MegaInfoo Cloud" class="logo-img">
            </div>
            
            <?php if ($resultado['sucesso']): ?>
                <!-- Resultado de Sucesso -->
                <div class="success-message">
                    <h2>✅ Cliente <strong><?php echo htmlspecialchars($resultado['cliente']); ?></strong> implantado com sucesso!</h2>
                    <p>Todas as configurações foram aplicadas no MikroTik.</p>
                    
                    <?php if (isset($resultado['bat_file']) && $resultado['bat_file']): ?>
                        <h3>📥 Arquivo .BAT gerado: 
                            <a href="<?php echo htmlspecialchars($resultado['bat_file']); ?>" download class="download-link">
                                Baixar usuarios_<?php echo htmlspecialchars($resultado['cliente']); ?>.bat
                            </a>
                        </h3>
                    <?php endif; ?>
                </div>
                
                <?php if (isset($resultado['log']) && !empty($resultado['log'])): ?>
                    <div class="log-section">
                        <h3>📋 Log de Execução</h3>
                        <pre><?php echo htmlspecialchars(print_r($resultado['log'], true)); ?></pre>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <!-- Resultado de Erro -->
                <div class="error-card">
                    <h2>❌ Erro na Implantação</h2>
                    <p><?php echo htmlspecialchars($resultado['erro']); ?></p>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="submit-btn" style="display: inline-block; text-decoration: none; width: auto; padding: 12px 30px;">
                    ← Nova Implantação
                </a>
            </div>
        </div>
    </div>

    <script>
        // Prevenir volta do navegador para evitar reprocessamento
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
        
        // Opcional: Limpar histórico após alguns segundos
        setTimeout(function() {
            if (window.history.replaceState) {
                window.history.replaceState(null, null, 'resultado.php');
            }
        }, 2000);
    </script>
</body>
</html>
