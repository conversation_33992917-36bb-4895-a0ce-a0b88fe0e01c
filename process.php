<?php
session_start();
if (!isset($_SESSION['autenticado'])) {
    header('Location: login.php');
    exit;
}
if (!isset($_POST['token']) || $_POST['token'] !== $_SESSION['token']) {
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Erro - MegaInfoo Cloud</title>
        <link rel="stylesheet" href="style.css">
    </head>
    <body>
        <div class="process-container">
            <div class="error-card">
                <h2>⚠️ Reenvio Bloqueado</h2>
                <p>Esta ação já foi executada ou é inválida.</p>
                <a href="index.php" style="color: #4A90E2; text-decoration: none; margin-top: 15px; display: inline-block;">← Voltar ao início</a>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}
unset($_SESSION['token']);

require 'vendor/autoload.php';
require('routeros_api.class.php');
require('config.php');

use PhpOffice\PhpSpreadsheet\IOFactory;

$cliente = strtoupper(trim($_POST['cliente']));
$ipbase = trim($_POST['ipbase']);
$apelido = strtolower(trim($_POST['apelido']));

$faixa = "$ipbase.0/24";
$gateway_ip = "$ipbase.1";
$gateway_address = "$gateway_ip/24";
$dns = "************";
$range_start = "$ipbase.100";
$range_end = "$ipbase.254";
$pool_name = "VPN $cliente";
$interface = "BRIDGE-REDE";

$log = [];

$API = new RouterosAPI();
if ($API->connect($mikrotik_host, $mikrotik_user, $mikrotik_pass, $mikrotik_port)) {
    $params = [
        "address" => $gateway_address,
        "interface" => $interface,
        "comment" => "GATEWAY $cliente"
    ];
    $response = $API->comm("/ip/address/add", $params);
    $log[] = ["Comando" => "/ip/address/add", "Params" => $params, "Resposta" => $response];

    $params = [
        "address" => $faixa,
        "gateway" => $gateway_ip,
        "dns-server" => $dns,
        "comment" => "REDE $cliente"
    ];
    $response = $API->comm("/ip/dhcp-server/network/add", $params);
    $log[] = ["Comando" => "/ip/dhcp-server/network/add", "Params" => $params, "Resposta" => $response];

    $params = [
        "name" => $pool_name,
        "ranges" => "$range_start-$range_end"
    ];
    $response = $API->comm("/ip/pool/add", $params);
    $log[] = ["Comando" => "/ip/pool/add", "Params" => $params, "Resposta" => $response];

    $params = [
        "name" => $pool_name,
        "local-address" => $gateway_ip,
        "remote-address" => $pool_name
    ];
    $response = $API->comm("/ppp/profile/add", $params);
    $log[] = ["Comando" => "/ppp/profile/add", "Params" => $params, "Resposta" => $response];

    if (isset($_FILES['secrets_file']) && $_FILES['secrets_file']['error'] === UPLOAD_ERR_OK) {
        $arquivo = $_FILES['secrets_file']['tmp_name'];
        $spreadsheet = IOFactory::load($arquivo);
        $sheet = $spreadsheet->getActiveSheet();

        $linha = 3;
        $bat = "@echo off\r\n";
        while (true) {
            $nome = trim($sheet->getCell("B$linha")->getValue());
            $senha = trim($sheet->getCell("C$linha")->getValue());

            if (empty($nome) || empty($senha)) {
                break;
            }

            $username = $apelido . "_" . $nome;

            $params = [
                "name" => $username,
                "password" => $senha,
                "service" => "any",
                "profile" => $pool_name
            ];
            $response = $API->comm("/ppp/secret/add", $params);
            $log[] = ["Comando" => "/ppp/secret/add", "Params" => $params, "Resposta" => $response];

            $bat .= "net user $nome $senha /add /passwordchg:no /expires:never\r\n";
            $bat .= "wmic useraccount where name='$nome' set PasswordExpires=FALSE\r\n";

            $linha++;
        }

        $bat_path = "usuarios_$cliente.bat";
        file_put_contents($bat_path, $bat);
        $bat_download = "<h3>📥 Arquivo .BAT gerado: <a href='$bat_path' download class='download-link'>Baixar usuarios_$cliente.bat</a></h3>";
    } else {
        $log[] = ["Erro" => "Erro ao fazer upload da planilha de secrets."];
        $bat_download = "";
    }

    $API->disconnect();

    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Implantação Concluída - MegaInfoo Cloud</title>
        <link rel="stylesheet" href="style.css">
    </head>
    <body>
        <div class="process-container">
            <div class="process-card">
                <div class="logo">
                    <svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="18" fill="#4A90E2" stroke="#fff" stroke-width="2"/>
                        <rect x="12" y="25" width="3" height="8" fill="#FF8C00" rx="1"/>
                        <rect x="17" y="22" width="3" height="11" fill="#FF8C00" rx="1"/>
                        <rect x="22" y="18" width="3" height="15" fill="#FF8C00" rx="1"/>
                        <path d="M8 15 Q20 8 32 15" stroke="#fff" stroke-width="2" fill="none"/>
                        <text x="45" y="18" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#4A90E2">megainffo</text>
                        <text x="45" y="32" font-family="Arial, sans-serif" font-size="12" fill="#666">.cloud</text>
                    </svg>
                </div>

                <div class="success-message">
                    <h2>✅ Cliente <strong><?php echo $cliente; ?></strong> implantado com sucesso!</h2>
                    <p>Todas as configurações foram aplicadas no MikroTik.</p>
                    <?php echo $bat_download; ?>
                </div>

                <div class="log-section">
                    <h3>📋 Log de Execução</h3>
                    <pre><?php echo htmlspecialchars(print_r($log, true)); ?></pre>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <a href="index.php" class="submit-btn" style="display: inline-block; text-decoration: none; width: auto; padding: 12px 30px;">
                        ← Nova Implantação
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
} else {
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Erro de Conexão - MegaInfoo Cloud</title>
        <link rel="stylesheet" href="style.css">
    </head>
    <body>
        <div class="process-container">
            <div class="error-card">
                <h2>❌ Erro ao conectar no MikroTik</h2>
                <p>Não foi possível estabelecer conexão com o roteador. Verifique as configurações de rede.</p>
                <a href="index.php" style="color: #4A90E2; text-decoration: none; margin-top: 15px; display: inline-block;">← Tentar novamente</a>
            </div>
        </div>
    </body>
    </html>
    <?php
}
