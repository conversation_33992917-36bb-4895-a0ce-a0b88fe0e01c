<?php

session_start();
if (!isset($_SESSION['autenticado'])) {
    header('Location: login.php');
    exit;
}
if (!isset($_POST['token']) || $_POST['token'] !== $_SESSION['token']) {
    die("<h2 style='color:red;'>Reenvio bloqueado: esta ação já foi executada ou é inválida.</h2>");
}
unset($_SESSION['token']);

require 'vendor/autoload.php';
require('routeros_api.class.php');
require('config.php');

use PhpOffice\PhpSpreadsheet\IOFactory;

$cliente = strtoupper(trim($_POST['cliente']));
$ipbase = trim($_POST['ipbase']);
$apelido = strtolower(trim($_POST['apelido']));

$faixa = "$ipbase.0/24";
$gateway_ip = "$ipbase.1";
$gateway_address = "$gateway_ip/24";
$dns = "************";
$range_start = "$ipbase.100";
$range_end = "$ipbase.254";
$pool_name = "VPN $cliente";
$interface = "BRIDGE-REDE";

$log = [];

$API = new RouterosAPI();
if ($API->connect($mikrotik_host, $mikrotik_user, $mikrotik_pass, $mikrotik_port)) {
    $params = [
        "address" => $gateway_address,
        "interface" => $interface,
        "comment" => "GATEWAY $cliente"
    ];
    $response = $API->comm("/ip/address/add", $params);
    $log[] = ["Comando" => "/ip/address/add", "Params" => $params, "Resposta" => $response];

    $params = [
        "address" => $faixa,
        "gateway" => $gateway_ip,
        "dns-server" => $dns,
        "comment" => "REDE $cliente"
    ];
    $response = $API->comm("/ip/dhcp-server/network/add", $params);
    $log[] = ["Comando" => "/ip/dhcp-server/network/add", "Params" => $params, "Resposta" => $response];

    $params = [
        "name" => $pool_name,
        "ranges" => "$range_start-$range_end"
    ];
    $response = $API->comm("/ip/pool/add", $params);
    $log[] = ["Comando" => "/ip/pool/add", "Params" => $params, "Resposta" => $response];

    $params = [
        "name" => $pool_name,
        "local-address" => $gateway_ip,
        "remote-address" => $pool_name
    ];
    $response = $API->comm("/ppp/profile/add", $params);
    $log[] = ["Comando" => "/ppp/profile/add", "Params" => $params, "Resposta" => $response];

    if (isset($_FILES['secrets_file']) && $_FILES['secrets_file']['error'] === UPLOAD_ERR_OK) {
        $arquivo = $_FILES['secrets_file']['tmp_name'];
        $spreadsheet = IOFactory::load($arquivo);
        $sheet = $spreadsheet->getActiveSheet();

        $linha = 3;
        $bat = "@echo off\r\n";
        while (true) {
            $nome = trim($sheet->getCell("B$linha")->getValue());
            $senha = trim($sheet->getCell("C$linha")->getValue());

            if (empty($nome) || empty($senha)) {
                break;
            }

            $username = $apelido . "_" . $nome;

            $params = [
                "name" => $username,
                "password" => $senha,
                "service" => "any",
                "profile" => $pool_name
            ];
            $response = $API->comm("/ppp/secret/add", $params);
            $log[] = ["Comando" => "/ppp/secret/add", "Params" => $params, "Resposta" => $response];

            $bat .= "net user $nome $senha /add /passwordchg:no /expires:never\r\n";
            $bat .= "wmic useraccount where name='$nome' set PasswordExpires=FALSE\r\n";

            $linha++;
        }

        $bat_path = "usuarios_$cliente.bat";
        file_put_contents($bat_path, $bat);
        echo "<h3>📥 Arquivo .BAT gerado: <a href='$bat_path' download>Baixar usuarios_$cliente.bat</a></h3>";
    } else {
        $log[] = ["Erro" => "Erro ao fazer upload da planilha de secrets."];
    }

    $API->disconnect();

    echo "<h2>Cliente <b>$cliente</b> implantado com sucesso!</h2>";
    echo "<h3>Log de Execução:</h3><pre>" . print_r($log, true) . "</pre>";
} else {
    echo "<b>Erro ao conectar no Mikrotik.</b>";
}
